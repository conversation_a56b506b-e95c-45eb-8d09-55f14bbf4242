'use client';
import { useState } from "react";
import Dr<PERSON><PERSON>ate<PERSON><PERSON> from "./dresscategory/dresscategory";
import Dress<PERSON>ist from "./dresslist/dresslist";

export default function AllProducts(){
    const [relationalId,setRelationalId] = useState<string | null>(null);

    return(
        <>
        <section className="mt-5 px-5 pb-20">
            <div className="grid grid-cols-2 gap-x-5 w-full">
                <div>
                    <DressCategory
                    idRelational={(idNum:string)=>{setRelationalId(idNum)}}
                    />
                </div>

                <div>
                    <DressList
                    relationalId={relationalId}
                    />
                </div>
            </div>
        </section>
        </>
    )
}