import DashLoading from "@/app/(dashboard)/loading";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google"
import { useEffect, useRef, useState } from "react";
import { FaCaretLeft } from "react-icons/fa";
import { IoIosEye } from "react-icons/io";
import { TbReportSearch } from "react-icons/tb";

type PropsType = {
    relationalId : string | null
}

type ProductListType = {
    data: DataType[],
    totalPage: number | null
}

type DataType = {
    id: string ,
    title: string | null,
    items: string | null
}

type ContainerType = {
    searchInput: string | null,
    currentPage: number,
    pages: number[] | null,
    perpage: number,
    offset: number
}

const anton = Anton({
    weight:"400",
    subsets:["latin"]
})

const manrope = Manrope({
    subsets:["latin"]
})

const temporaryPage = [0,1,2,3,4,5,6,7];

export default function DressList({relationalId}:PropsType){
    const searchRef = useRef<HTMLInputElement>(null);
    const [container,setContainer] = useState<ContainerType>({
        searchInput: null,
        currentPage: 1,
        pages: null,
        perpage: 6,
        offset: 0
    })

    const {isLoading,isError,data} = useQuery<ProductListType>({
        queryKey:["allProductsList",relationalId,container.searchInput,container.offset],
        queryFn:async ()=>{
            const url = container.searchInput || container.offset ? `/api/productslist?relationid=${relationalId}&search=${container.searchInput}&offset=${container.offset}` : `/api/productslist?relationid=${relationalId}`;

            const getData = await axios(url);
            const response= getData.data;

            return response
        },
        enabled: !!relationalId
    });
    
    const handleSearchInput=()=>{
        const value = searchRef.current?.value ?? null;

        setContainer(prev=>({...prev,searchInput:value}))
    }

    const currentPage=(currentPage:number)=>{
        const offset = (container.perpage * currentPage) - currentPage;

        setContainer(prev=>({...prev,currentPage,offset}))
    }

    useEffect(()=>{
        if(data?.totalPage){
            const totalpage = data.totalPage;
            const pages = [...Array(totalpage)].map((_,index)=>index + 1);

            setContainer(prev=>({...prev,pages}));
        }
    },[data?.totalPage])
    return(
        <>
        <section>
            <div className="text-right">
                <h2 className={`${anton.className} text-4xl uppercase tracking-wider text-transparent`} style={{WebkitTextStroke:'1px black'}}>
                    dress list
                </h2>
            </div>

            <div className="flex flex-row justify-end items-end gap-x-2 mt-5">
                <div className="h-[30px] w-[50%]">
                    <input type="text"  placeholder="search" className={`${manrope.className} h-full w-full shadow-[0px_1px_0px_black]/20 px-2 placeholder:text-black/20 placeholder:font-medium focus:outline-none text-black/80`} ref={searchRef}/>
                </div>
                <div>
                    <button className={`${manrope.className} border border-t-0 border-r-0 border-l-0 border-b-black/80 rounded-sm shadow-[0px_0px_5px_black]/20 px-2 text-black/50 transition-all ease-linear duration-150 hover:cursor-pointer hover:bg-black hover:text-white/80`} onClick={handleSearchInput}>
                        search
                    </button>
                </div>
            </div>

           <div className="mt-5">
                           {
                               isLoading?
                               <div className="w-full h-[100px] flex justify-center items-center mt-5">
                                   <DashLoading/>
                               </div>:
                               isError?
                               <div>
                                   <h4>
                                       error message will update soon
                                   </h4>
                               </div>:
                               data ?
                               <table className="w-full table-fixed border border-black/80">
                               <thead className={`${manrope.className} bg-black text-white capitalize h-10`}>
                                   <tr >
                                       <th className="text-left px-2">
                                           id
                                       </th>
                                       <th className="text-left px-2">
                                           title
                                       </th>
                                       <th className="text-left px-2">
                                           items
                                       </th>
                                       <th className="w-[10%]">

                                       </th>
                                   </tr>
                               </thead>
                                <tbody className={`${manrope.className} text-black/50 font-medium -mt-5`}>
                                   {
                                       data?.data?.map((items:DataType,index:number)=>{
                                           return <tr className="even:bg-black/10 transition-all duration-150 ease-linear hover:text-black group/show hover:cursor-pointer" key={index}>
                                       <td className="w-full truncate px-2 py-5">
                                           {items.id}
                                       </td>
                                       <td className="w-full truncate px-2 py-5">
                                           {items.title}
                                       </td>
                                       <td className="w-full truncate px-2 py-5 flex flex-row items-center justify-between">
                                           {items.items}
                                       </td>
                                       <td className="transition-all duration-150 ease-linear group-hover/show:text-2xl">
                                            <TbReportSearch />
                                       </td>
                                   </tr>
                                       })
                                   }
                               </tbody>
                               </table>:
                               <div className="flex flex-row items-center justify-end">
                                <span>
                                    <FaCaretLeft />
                                </span>
                                <h5 className={`${anton.className} uppercase`}>
                                    select a row from the dress category
                                </h5>
                               </div>
                           }
                       </div>

            <div className="mt-5 w-full flex flex-row gap-x-2.5 justify-end">
                {
                    container?.pages?.map((items,index)=>{
                        return <button className={`${manrope.className} border border-black/20 rounded-sm h-[30px] w-[30px] flex justify-center items-center text-black/50 transition-all duration-150 ease-linear hover:cursor-pointer hover:bg-black/20 hover:text-black hover:border-none ${index+1 === container.currentPage?"bg-black/20 text-black border-none":"bg-transparent"}`} key={index} onClick={()=>{currentPage(items)}}>
                            {items}
                        </button>
                    })
                }
            </div>
        </section>
        </>
    )
}