"use client";
import DashLoading from "@/app/(dashboard)/loading";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { <PERSON>, Manrope } from "next/font/google"
import { useEffect, useRef, useState } from "react";
import { IoIosEye } from "react-icons/io";

type PropsType = {
    idRelational: (idNum:string)=>void;
}

type ProductsCategoryData = {
    data:Data[],
    totalPage: number
};

type Data = {
    id:string,
    class: string | null,
    dresscategory: DressCategory
}

type DressCategory = {
    id: string,
    category: string | null
}

type ContainerType = {
    totalPage: number[] | null,
    currentPage : number,
    searchInput : string | null,
    perpage : number,
    offset : number
}

type ProductsInfo = {
    id: string,
    category:string | null,
    dressclass:{id:string,class:string|null}
}

const anton = Anton({
    weight:"400",
    subsets:["latin"]
})

const manrope = Manrope({
    subsets:["latin"]
})

export default function DressCategory({idRelational}:PropsType){
    const searchRef = useRef<HTMLInputElement>(null);
    const [container,setContainer] = useState<ContainerType>({
        totalPage:[],
        currentPage:1,
        searchInput: null,
        perpage: 4,
        offset: 0
    })

    const {isLoading,isError,data} = useQuery<ProductsCategoryData>({
        queryKey:["allProductsCategroy",container.searchInput,container.offset],

        queryFn:async ()=>{
            const url = container.searchInput || container.offset ? `/api/productscategory?search=${container.searchInput}&offset=${container.offset}` : "/api/productscategory"
            const getData = await axios.get(url);
            const response= getData.data;

            return response
        },
        
    });

    const handleSearch=()=>{
        const value = searchRef.current?.value ?? null;

        setContainer(prev=>({...prev,searchInput:value}));
    }

    const currentPage=(currentPage:number)=>{
        const offset = (container.perpage * currentPage) - container.perpage;

        setContainer(prev=>({...prev,offset,currentPage}));
    }

    useEffect(()=>{
        if(data?.totalPage){
            const totalPage = [...Array(data.totalPage)].map((_,index)=>index + 1);

            setContainer(prev=>({...prev,totalPage}))
        }
    },[data])
    return(
        <>
        <section>
            <div>
                <h2 className={`${anton.className} text-4xl uppercase tracking-wider text-transparent`} style={{WebkitTextStroke:'1px black'}}>
                    dress category
                </h2>
            </div>

            <div className="flex flex-row justify-end items-end gap-x-2 mt-5">
                <div className="h-[30px] w-[50%]">
                    <input type="text"  placeholder="search" className={`${manrope.className} h-full w-full shadow-[0px_1px_0px_black]/20 px-2 placeholder:text-black/20 placeholder:font-medium focus:outline-none text-black/80`} ref={searchRef}/>
                </div>
                <div>
                    <button className={`${manrope.className} border border-t-0 border-r-0 border-l-0 border-b-black/80 rounded-sm shadow-[0px_0px_5px_black]/20 px-2 text-black/50 transition-all ease-linear duration-150 hover:cursor-pointer hover:bg-black hover:text-white/80`} onClick={handleSearch}>
                        search
                    </button>
                </div>
            </div>

            <div className="mt-5">
                {
                    isLoading?
                    <div className="w-full h-[100px] flex justify-center items-center mt-5">
                        <DashLoading/>
                    </div>:
                    isError?
                    <div>
                        <h4>
                            error message will update soon
                        </h4>
                    </div>:
                    <table className="w-full table-fixed border border-black/80">
                    <thead className={`${manrope.className} bg-black text-white capitalize h-10`}>
                        <tr >
                            <th className="text-left px-2">
                                id
                            </th>
                            <th className="text-left px-2">
                                category
                            </th>
                            <th className="text-left px-2">
                                style
                            </th>
                        </tr>
                    </thead>
                     <tbody className={`${manrope.className} text-black/50 font-medium -mt-5`}>
                        {
                            data?.data?.map((items,index)=>{
                                return <tr className="even:bg-black/10 transition-all duration-150 ease-linear hover:text-black group/show hover:cursor-pointer" key={index} onClick={()=>{idRelational(items.id)}}>
                            <td className="w-full truncate px-2 py-5">
                                {items.dresscategory.id}
                            </td>
                            <td className="w-full truncate px-2 py-5">
                                {items.dresscategory.category}
                            </td>
                            <td className="w-full truncate px-2 py-5 flex flex-row items-center justify-between">
                               {items.class}

                                <span className="opacity-0 transition-all duration-150 ease-linear group-hover/show:opacity-100 text-xl">
                                    <IoIosEye />
                                </span>
                            </td>
                        </tr>
                            })
                        }
                    </tbody>
                    </table>
                }
            </div>

            <div className="mt-5 w-full flex flex-row gap-x-2.5 justify-end">
                {
                    container.totalPage?.map((items,index)=>{
                        return <button className={`${manrope.className} border border-black/20 rounded-sm h-[30px] w-[30px] flex justify-center items-center text-black/50 transition-all duration-150 ease-linear hover:cursor-pointer hover:bg-black/20 hover:text-black hover:border-none ${index+1 === container.currentPage?"bg-black/20 text-black border-none":"bg-transparent"}`} key={index} onClick={()=>{currentPage(items)}}>
                            {items}
                        </button>
                    })
                }
            </div>
        </section>
        </>
    )
}