"use client";
import DashLoading from "@/app/(dashboard)/loading";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { <PERSON>, Manrope } from "next/font/google"
import Image from "next/image";
import { useParams } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";
import { AiOutlineUnorderedList } from "react-icons/ai";
import { BiItalic } from "react-icons/bi";
import { ImBold, ImCross } from "react-icons/im";
import { PiTextAUnderlineBold } from "react-icons/pi";
import { TbCapture } from "react-icons/tb";

type ProductDetails = {
    title?: string | null,
    items?: string | null,
    regular?: number| null,
    discount?:number| null,
    discrate?:number| null,
    brandtitle?:string| null,
    brandimg?:File | string | null,
    colorCode?: string[] | null,
    colorCodeInput?: string| null,
    dressSize?: string[] | null,
    dressSizeInput?:string | null,
    parenttable?: string | null,
    details?: string | null,
    storeImg:StoreImage
}

type SponserData = {
    id: number,
    title: string | null,
    imgpath: string | null
}

type StoreImage = {
    imgFile1: File | null,
    imgFile2: File | null,
    imgFile3: File | null,
    imgFile4: File | null,
}

type PrevalueInput = {
    items : string[] | null,
    colorCode : string[] | null,
    size : string[] | null
}

type Restore = {
    img: boolean,
    color: boolean,
    size: boolean
}

const manrope = Manrope({
    subsets:["latin"]
})

const anton = Anton({
    subsets: ["latin"],
    weight: "400"
})
export default function AddDetails() {
    const params = useParams();
    // const addDetailsParams = params.adddetails as string;
    const detailsParams = JSON.parse(decodeURIComponent(params.addDetailsParams as string))
    const queryClinet = useQueryClient();
    console.log(detailsParams);

    const [productDetails,setDetails] = useState<ProductDetails>({storeImg:{
        imgFile1:null,
        imgFile2:null,
        imgFile3:null,
        imgFile4:null},
        parenttable: null
    });

    const [editorTxt,setEditorTxt] = useState<string>("");
    const editorRef = useRef<HTMLDivElement|null>(null);
    const [prevaluInput,setPrevalue] = useState<PrevalueInput>({
        items: null,
        colorCode: null,
        size: null
    })
    const [restore,setRestore] = useState<Restore>({img:false,color:false,size:false});

    const {isLoading:sponserLoad,isError:sponserError,data:sponserData} = useQuery<SponserData[]>({
        queryKey:["sponserImg"],
        queryFn: async ()=>{
            const response = await axios("/api/sponserdataretrieve");
            const result = response.data.data;
            
            return result as SponserData[];
        }
    });

    const {isLoading:customeLoad,isError:customeError,data:customeData} = useQuery({
        queryKey:["customeProductsData"],
        queryFn:async()=>{
            const getData = await axios("/api/customeproductsdata");
            const response= getData.data;

            return response;
        }
    });

    const addProduct = useMutation({
        mutationFn:async (formData:FormData)=>{
            const post = await axios.post("/api/productsdata",formData);
            const response = post;
            if(response.status == 200){
                productImg(response.data.code);
                productColor(response.data.code);
                productSize(response.data.code);
            }
        }
    });

    const addProductImg = useMutation({
        mutationFn:async (formData:FormData)=>{
            const post = await axios.post("/api/productsimg",formData);
            const response = post;

            if(response.status === 200){
                setRestore(prev=>({...prev,img:true}))
            }
        }
    });

    const addProductColor = useMutation({
        mutationFn:async (formData:FormData)=>{
            const post = await axios.post("/api/productscolor",formData);
            const response = post;

            if(response.status === 200){
                setRestore(prev=>({...prev,color:true}))
            }
        }
    });

    const addProductSize = useMutation({
        mutationFn:async (formData:FormData)=>{
            const post = await axios.post("/api/productssize",formData);
            const response = post;

            if(response.status === 200){
                setRestore(prev=>({...prev,size:true}))
            }
        }
    });

    const productImg = (parenttable:string)=>{
        const copy = productDetails.storeImg;
        const wrap ={...copy,parenttable};
        const formData = new FormData();

        Object.entries(wrap).forEach(([key,value])=>{
            if(typeof value == "string"){
                formData.append(key,value)
            }

            if(value instanceof File){
                formData.append(key,value)
            }
        });

        addProductImg.mutate(formData);
    }

    const productColor = (parenttable:string)=>{
        const copy = productDetails.colorCode;
        
        const update = copy?.map((items,index)=>({
            [`title[${index}]`] : items
        })) ?? [];

        const formData = new FormData();

        [...update,{parenttable}].map((items)=>{
            Object.entries(items).forEach(([key,value])=>{
                formData.append(key,value)
            })
        });

        addProductColor.mutate(formData);
    }

    const productSize = (parenttable:string)=>{
        const copy = productDetails.dressSize;
        const update = copy?.map((items,index)=>({
            [`title[${index}]`] : items
        })) ?? [];

        const formData = new FormData();

        [...update,{parenttable}].map((items)=>{
            Object.entries(items).forEach(([key,value])=>{
                formData.append(key,value)
            })
        })

        addProductSize.mutate(formData)
    }

    const textEditorInput=(event:React.FormEvent<HTMLDivElement>)=>{
        setDetails({...productDetails,details:event.currentTarget.innerHTML})
    }

    const handlerInputChange=(event:React.ChangeEvent<HTMLInputElement>)=>{
        const {name,value,files} = event.target;
        const existed = files?.[0];

        if(name == "brandimg"){
            const copy = productDetails?.brandtitle;
            const match = sponserData?.filter((items)=>items.title == copy);

            setDetails({...productDetails,brandimg:existed,brandtitle: match && match.length > 0? "" : copy});
        }else{
            setDetails({...productDetails,[name]:value});
        }
    }

    const handleImgInputChange=(event:React.ChangeEvent<HTMLInputElement>)=>{
        const {name,files} = event.target;
        const existed = files?.[0];

        setDetails({...productDetails,storeImg:{...productDetails?.storeImg,[name]:existed}})
    }

    const reuseableInput=(placeholder:string | null,value:string | number | null,name:string)=>{
        return <>
        <input type="text" value={value ?? ""} name={name} autoComplete="off" className={`${manrope.className} h-full w-full px-3 placeholder:capitalize placeholder:text-black/40 placeholder:font-bold text-black/80 font-medium focus:outline-black/10 bg-white rounded-lg shadow-xs shadow-black/10`} placeholder={`${placeholder}`}
        onChange={(event)=>{handlerInputChange(event)}}
        />
        </>
    }

    const getValue=(condition:string)=>{
        const copy = condition == "colorCodeInput" ? (productDetails.colorCode ?? []) : (productDetails.dressSize ?? []);
        
        const update = condition == "colorCodeInput" ? [...copy,productDetails.colorCodeInput ?? ""] : [...copy,productDetails.dressSizeInput ?? ""];

        if(condition == "colorCodeInput"){
            setDetails({...productDetails,colorCodeInput:"",colorCode:update})
        }else{
            setDetails({...productDetails,dressSizeInput:"",dressSize:update})
        }
    }

    const removeValue=(condition:string,indexNum:number)=>{
        const copy = condition == "colorCode" ? productDetails.colorCode : productDetails.dressSize;

        const update = copy?.filter((_,index)=> index !== indexNum);

        if(condition == "colorCode"){
            setDetails({...productDetails,colorCode:update})
        }else[
            setDetails({...productDetails,dressSize:update})
        ]
    }

    const brandConfig = (title:string|null)=>{
        const getFile = sponserData?.filter((items)=> items.title == title)?.[0].imgpath;

        setDetails({...productDetails,brandimg:getFile,brandtitle:title})
    }

    const textEditorAction=(action:string)=>{
        document.execCommand(action,false);
    }

    const addNewProduct=()=>{
        const formData = new FormData();

        Object.entries(productDetails).forEach(([key,value])=>{
            if(key == "colorCode" || key == "dressSize" || key == "storeImg" || key == "colorCodeInput" || key == "dressSizeInput"){
                return;
            }else if(value == null){
                return;
            }else if(typeof value == "number"){
                formData.append(key,String(value))
            }else if(typeof value == "string"){
                formData.append(key,value)
            }else if(value instanceof File){
                formData.append(key,value)
            }
        });

        addProduct.mutate(formData);
    }

    useEffect(()=>{
        function calculateDiscountRate(){
            const regular = productDetails?.regular ?? 0;
            const discount= productDetails?.discount?? 0;

            const rate = (regular - discount) / regular;
            const percentage = Number((rate * 100).toFixed(2));

            setDetails({...productDetails,discrate: isNaN(percentage) ? null : percentage});
        }

        calculateDiscountRate();
    },[productDetails?.regular,productDetails?.discount]);

    useEffect(()=>{
        if(restore.color && restore.img && restore.size){
            setDetails({storeImg:{
                imgFile1: null,
                imgFile2: null,
                imgFile3: null,
                imgFile4: null
            },
            parenttable: null
        });

            if(editorRef.current){
                editorRef.current.innerHTML = ""
            }

            setRestore({img:false,color:false,size:false});

            queryClinet.invalidateQueries({queryKey:["customeProductsData"]})
        }
    },[restore]);

    useEffect(()=>{
        if(customeData){
            const items = [...new Set(customeData.map((items: { items: string; })=>items.items))] as string[];

            const colorCode = [...new Set(customeData.flatMap((items: { productcolor: string; })=>items.productcolor).map((items: { title: string; })=>items.title))] as string[];

            const size = [...new Set(customeData.flatMap((items: { productsize: string; })=>items.productsize).map((items: { title: string; })=>items.title))] as string[];

            setPrevalue({colorCode,size,items})
        }
    },[customeData])
    return (
        <>
        <section className="mt-5 px-5 pb-20">
            <div className="grid grid-cols-2 gap-x-5">
                <div>
                    <div className="flex flex-row gap-x-5">
                        {(Object.keys(productDetails.storeImg) as Array<keyof StoreImage>).map((items,index)=>{
                            return <div className="h-[120px] w-full shadow-xs shadow-black/20 rounded-lg bg-white relative overflow-hidden" key={index}>
                            {
                                productDetails.storeImg[`${items}`]?
                                <label htmlFor={`${items}`} className="absolute h-full w-full flex justify-center items-center">
                                <input type="file" name={`${items}`} id={`${items}`} accept="image/*" className="hidden"
                                onChange={(event)=>{handleImgInputChange(event)}}
                                />
                                <Image src={productDetails.storeImg[`${items}`] instanceof File ? URL.createObjectURL(productDetails.storeImg[`${items}`] as File): ""} alt="productImage" fill/>
                                <span className="text-4xl text-black/20">
                                    <TbCapture />
                                </span>
                            </label>:
                            <label htmlFor={`${items}`} className="absolute h-full w-full flex justify-center items-center">
                                <input type="file" name={`${items}`} id={`${items}`} accept="image/*" className="hidden"
                                onChange={(event)=>{handleImgInputChange(event)}}
                                />
                                
                                <span className="text-4xl text-black/20">
                                    <TbCapture />
                                </span>
                            </label>
                            }
                        </div>
                        })}
                    </div>

                    <div className="mt-10">
                        <h2 className={`${anton.className} uppercase text-4xl font-bold tracking-widest`} style={{WebkitTextStroke:'2px black',WebkitTextFillColor:"transparent"}}>
                            details
                        </h2>

                        <div className={`${manrope.className} mt-5 h-[250px] w-full p-2 resize-none focus:outline-black/20 text-black/80 text-base rounded-lg bg-white shadow-sm shadow-black/20 textEditor overflow-y-auto`} contentEditable suppressContentEditableWarning ref={editorRef} onInput={(event)=>{textEditorInput(event)}}>
                            
                        </div>
                        <div>
                            <pre>
                                {editorTxt}
                            </pre>
                        </div>
                        <div className="flex flex-row gap-x-5 mt-5 items-center justify-end px-4">
                            <button className="p-[10px] bg-white rounded-xl transition-all duration-150 ease-linear hover:cursor-pointer shadow-[1px_1px_4px_black]/20 hover:shadow-[4px_4px_2px_black]/50" onClick={()=>{textEditorAction("bold")}}>
                                <ImBold />
                            </button>
                            <button className="p-[10px] bg-white rounded-xl transition-all duration-150 ease-linear hover:cursor-pointer shadow-[1px_1px_4px_black]/20 hover:shadow-[4px_4px_2px_black]/50"  onClick={()=>{textEditorAction("italic")}}>
                                <BiItalic />
                            </button>
                            <button className="p-[10px] bg-white rounded-xl transition-all duration-150 ease-linear hover:cursor-pointer shadow-[1px_1px_4px_black]/20 hover:shadow-[4px_4px_2px_black]/50"  onClick={()=>{textEditorAction("underline")}}>
                                <PiTextAUnderlineBold />
                            </button>
                            <button className="p-[10px] bg-white rounded-xl transition-all duration-150 ease-linear hover:cursor-pointer shadow-[1px_1px_4px_black]/20 hover:shadow-[4px_4px_2px_black]/50"  onClick={()=>{textEditorAction("insertUnorderedList")}}>
                                <AiOutlineUnorderedList />
                            </button>
                        </div>
                    </div>
                </div>

                <div>
                    <div className="flex flex-row gap-x-5">
                        <div className="h-10 w-full">
                            {reuseableInput("title",productDetails?.title ?? "","title")}
                        </div>
                        <div className="h-10 w-full relative group">
                            {reuseableInput("items",productDetails?.items ?? "","items")}

                            <div className="absolute w-full rounded-xl top-10 py-2 opacity-0 pointer-events-none transition-all duration-200 ease-linear group-hover:opacity-100 group-hover:pointer-events-auto group-hover:z-20">
                                <div className="py-5 px-2.5 bg-white shadow-sm shadow-black/20 rounded-xl flex flex-col">
                                    {
                                        prevaluInput.items?.map((items,index)=>{
                                            return <span className={`${manrope.className} text-black/20 transition-all duration-150 ease-linear hover:cursor-pointer hover:text-black`} key={index} onClick={()=>{setDetails(prev=>({...prev,items:items}))}}>
                                                {items}
                                            </span>
                                        }) ??
                                        <span className={`${manrope.className} text-black/20`}>
                                        No items found
                                        </span>
                                    }
                                    
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="mt-5">
                        <div className="flex flex-row gap-x-5 items-center">
                            <div>
                                <h3 className={`${anton.className} text-2xl font-medium tracking-wider`}>
                                    Color
                                </h3>
                            </div>
                            <div className="h-10 w-full relative group">
                            {reuseableInput("color code",productDetails?.colorCodeInput ?? "","colorCodeInput")}

                            <div className="absolute w-full rounded-xl top-10 py-2 opacity-0 pointer-events-none transition-all duration-200 ease-linear group-hover:opacity-100 group-hover:pointer-events-auto group-hover:z-20">
                                <div className="py-5 px-2.5 bg-white shadow-sm shadow-black/20 rounded-xl flex flex-col gap-y-2">
                                    {
                                        prevaluInput.colorCode?.map((items,index)=>{
                                            return <span className={`${manrope.className} text-black/20 transition-all duration-150 ease-linear hover:cursor-pointer hover:text-black flex flex-row gap-x-5 items-center`} key={index} onClick={()=>{setDetails(prev=>({...prev,colorCodeInput:items}))}}>

                                                <span className="h-10 w-10 rounded-full" style={{backgroundColor:`${items}`}}>

                                                </span>

                                                {items}
                                            </span>
                                        }) ??
                                        <span className={`${manrope.className} text-black/20`}>
                                        No items found
                                        </span>
                                    }
                                    
                                </div>
                            </div>
                            </div>

                            <div className="w-full flex flex-row justify-end">
                                <button className={`${manrope.className} bg-[#2ecc71] px-3 py-2 rounded-xl text-white font-bold shadow-[2px_2px_2px_#16a085] transition-all duration-200 ease-linear hover:cursor-pointer active:scale-95 hover:shadow-[1px_1px_1px_#16a085] hover:bg-[#27ae60]`} onClick={()=>{getValue("colorCodeInput")}}>
                                    add
                                </button>
                            </div>
                        </div>

                        <div className="mt-[10px] w-full px-5 py-5 bg-white rounded-lg shadow-xs shadow-black/20 flex flex-row flex-wrap gap-x-5 gap-y-5">
                            {
                                productDetails?.colorCode?.map((items,index)=>{
                                    return <div key={index} className="h-10 w-10 rounded-full relative" style={{backgroundColor:`${items}`}}>
                                        <button className="absolute text-xs -top-2.5 -right-1.5 text-rose-200 transition-all duration-150 ease-linear hover:cursor-pointer hover:text-rose-500" onClick={()=>{removeValue("colorCode",index)}}>
                                            <ImCross />
                                        </button>
                                    </div>
                                })
                            }
                        </div>
                    </div>

                    <div className="mt-5">
                        <div className="flex flex-row gap-x-5 items-center">
                            <div>
                                <h3 className={`${anton.className} text-2xl font-medium tracking-wider`}>
                                    size
                                </h3>
                            </div>
                            <div className="h-10 w-full relative group">
                            {reuseableInput("dress size",productDetails?.dressSizeInput ?? "", "dressSizeInput")}

                            <div className="absolute w-full rounded-xl top-10 py-2 opacity-0 pointer-events-none transition-all duration-200 ease-linear group-hover:opacity-100 group-hover:pointer-events-auto group-hover:z-20">
                                <div className="py-5 px-2.5 bg-white shadow-sm shadow-black/20 rounded-xl flex flex-col gap-y-2">
                                    {
                                        prevaluInput.size?.map((items,index)=>{
                                            return <span className={`${manrope.className} text-black/20 transition-all duration-150 ease-linear hover:cursor-pointer hover:text-black flex flex-row gap-x-5 items-center`} key={index} onClick={()=>{setDetails(prev=>({...prev,dressSizeInput:items}))}}>
                                                {items}
                                            </span>
                                        }) ??
                                        <span className={`${manrope.className} text-black/20`}>
                                        No items found
                                        </span>
                                    }
                                    
                                </div>
                            </div>
                            </div>

                            <div className="w-full flex flex-row justify-end">
                                <button className={`${manrope.className} bg-[#2ecc71] px-3 py-2 rounded-xl text-white font-bold shadow-[2px_2px_2px_#16a085] transition-all duration-200 ease-linear hover:cursor-pointer active:scale-95 hover:shadow-[1px_1px_1px_#16a085] hover:bg-[#27ae60]`} onClick={()=>{getValue("dressSizeInput")}}>
                                    add
                                </button>
                            </div>
                        </div>

                        <div className="mt-[10px] w-full px-5 py-5 bg-white rounded-lg shadow-xs shadow-black/20 flex flex-row flex-wrap gap-x-5 gap-y-5">
                            {
                                productDetails?.dressSize?.map((items,index)=>{
                                    return <div key={index} className="bg-[#bdc3c7]/80 rounded-full px-3 py-2 shadow-[3px_2px_2px_#dfe6e9] relative">
                                        <span className={`${manrope.className} text-sm font-bold text-[#222f3e] tracking-wide`}>
                                            {items}
                                        </span>

                                        <button className="absolute text-xs -top-2.5 -right-1.5 text-rose-200 transition-all duration-150 ease-linear hover:cursor-pointer hover:text-rose-500" onClick={()=>{removeValue("dressSize",index)}}>
                                            <ImCross />
                                        </button>
                                    </div>
                                })
                            }
                        </div>
                    </div>

                    <div className="flex flex-row gap-x-5 mt-5">
                        <div className="h-10 w-full">
                            {reuseableInput("regular price",productDetails?.regular ?? "","regular")}
                        </div>

                        <div className="h-10 w-full">
                            {reuseableInput("discount price",productDetails?.discount ?? "","discount")}
                        </div>

                        <div className="h-10 w-full">
                            {reuseableInput("discount rate",productDetails?.discrate ?? "","discrate")}
                        </div>
                    </div>

                    <div className="mt-5">
                        <div className="flex flex-row gap-x-5 items-center">
                            <div className="w-full">
                                <h3 className={`${anton.className} text-2xl font-medium tracking-wider`}>
                                    Brand title
                                </h3>
                            </div>
                            <div className="h-10 w-full relative group">
                            {reuseableInput("brand title",productDetails?.brandtitle ?? "","brandtitle")}

                            <div className="absolute w-full rounded-xl top-10 py-2 opacity-0 pointer-events-none transition-all duration-200 ease-linear group-hover:opacity-100 group-hover:pointer-events-auto z-20">
                                {
                                    sponserLoad?
                                    <div>
                                        <DashLoading/>
                                    </div>:
                                    sponserError?
                                    <div>
                                        <h4>something went wrong</h4>
                                    </div>:
                                    <div className="flex flex-col gap-y-2.5 bg-white shadow-sm shadow-black/20 rounded-xl py-5 px-5">
                                        {
                                            sponserData?.map((items,index)=>{
                                                return <span className={`${manrope.className} text-black/50 transition-all duration-150 ease-linear hover:bg-black/20 px-4 rounded-lg`} key={index} onClick={()=>{brandConfig(items.title)}}>
                                                    {items.title}
                                                </span>
                                            }) ?? 
                                            <div className="py-5 px-2.5 bg-white shadow-sm shadow-black/20 rounded-xl">
                                                <span className={`${manrope.className} text-center text-black/20`}>
                                                    No items found
                                                </span>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                            </div>

                            <div className="w-[5%]">
                                
                            </div>
                        </div>

                        <div className="mt-[10px] w-full h-20 bg-white rounded-xl shadow-xs shadow-black/20 relative overflow-hidden group">
                            <label htmlFor="brandimg" className="h-full w-full absolute flex justify-center items-center">
                                <input type="file" name="brandimg" id="brandimg" accept="image/*" className="hidden" onChange={(event)=>{handlerInputChange(event)}}/>

                                {
                                    productDetails?.brandimg ? 
                                    <Image src={productDetails.brandimg instanceof File ? URL.createObjectURL(productDetails.brandimg): `${productDetails.brandimg}`} alt="brandimg" fill className=" h-[90%] object-contain"/>
                                    : null
                                }

                                <span className="text-4xl text-black/20 transition-all duration-150 ease-linear group-hover:text-black group-hover:scale-105">
                                    <TbCapture />
                                </span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div className="mt-10">
                <button className={`${anton.className} bg-[#3498db] px-4 py-2 text-white tracking-widest text-xl rounded-xl shadow-[4px_3px_2px_#2980b9] transition-all duration-75 ease-linear hover:shadow-[3px_3px_4px_#3498db] hover:bg-[#2980b9] hover:cursor-pointer`} onClick={addNewProduct}>
                    add new product
                </button>
            </div>
        </section>
        </>
    )
}