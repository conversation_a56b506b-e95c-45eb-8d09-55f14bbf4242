"use client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { <PERSON>, <PERSON>rop<PERSON> } from "next/font/google"
import { useRouter } from "next/navigation";
import { useRef, useState } from "react";
import { FaCheck } from "react-icons/fa";
import { FaPersonHalfDress } from "react-icons/fa6";
import { GiHamburgerMenu } from "react-icons/gi";
import { ImCross } from "react-icons/im";
import { MdDelete, MdModeEditOutline } from "react-icons/md";3

type ProductDetails = {
    id: string | null,
    category: string,
    class : ClassProps[]
}

type ClassProps = {
    id: string | null,
    class:string | null,
    parenttable:string | null
}

type DressInput = {
    [key:number] : string
}

type ToggleEdditArr = {
    [key:number] : number
}
const manrope = Manrope({
    subsets:['latin']
});

const anton = Anton({
    subsets: ['latin'],
    weight: "400"
})


export default function AddProducts(){
    const [productDetails,setProductDetails] = useState<ProductDetails[]>([]);
    const categoryRef = useRef<HTMLInputElement | null>(null);
    const [dressInput,setDressInput] = useState<DressInput>({});
    const [accordionArr,setAccordionArr] = useState<number[]>([]);
    const [editArray,setEditArray] = useState<ToggleEdditArr[]>([]);
    const [categoryArray,setCategoryArray] = useState<number[]>([]);
    const queryClient = useQueryClient();
    const router = useRouter();

    const {isLoading:categoryLoad,isError:categoryError,data:categoryData} = useQuery({
        queryKey:["dressCategory"],
        queryFn:async()=>{
            const getData = await axios("/api/getdresscategory");
            const response= getData.data;

            setProductDetails(response);

            return response;
        }
    });
    
    const addDressClass= useMutation({
        mutationFn:async(formData:FormData)=>{
            const post = await axios.post("/api/dressclass",formData);
            const response= post.data;

            return response
        },

        onSuccess:()=>{queryClient.invalidateQueries({queryKey:["dressCategory"]})}
    })

    const removeDressClass = useMutation({
        mutationFn:async(id:string)=>{
            await axios.delete("/api/removedressclass",{params:{id}})
        },

        onSuccess:()=>{queryClient.invalidateQueries({queryKey:["dressCategory"]})}
    });

    const updateDressClass = useMutation({
        mutationFn:async(formData:FormData)=>{
            await axios.put("/api/updatedressclass",formData)
        }
    });

    const addCategory = useMutation({
        mutationFn: async (formData:FormData)=>{
            const post = await axios.post("/api/adddresscategory",formData);
            const res = post;

            console.log(res);
        },

        onSuccess:()=>{queryClient.invalidateQueries({queryKey:["dressCategory"]})}
    })

    const removeCategory = useMutation({
        mutationFn:(serial:string | null)=>{
            return axios.delete("/api/removedresscategory",{params:{id:serial}})
        },

        onSuccess:()=>{queryClient.invalidateQueries({queryKey:["dressCategory"]})}
    });

    const updateCategory = useMutation({
        mutationFn:async(formData:FormData)=>{
            const update =await axios.put("/api/updatedresscategory",formData);
            const response= update;

            console.log(response);

            return response;
        },

        onSuccess:()=>{queryClient.invalidateQueries({queryKey:["dressCategory"]})}
    });

    const addNewCategory=()=>{
        if(categoryRef?.current?.value){
            const formData = new FormData();
            const copy = productDetails;
            const update = [...copy,{id:null,category:categoryRef.current.value,class:[]}];

            setProductDetails(update)

            formData.append("category",categoryRef.current.value);

            addCategory.mutate(formData);
            categoryRef.current.value = "";
        }
    }

    const handleDressInput=(key:number,event: React.ChangeEvent<HTMLInputElement>)=>{
        const {value} = event.target;

        setDressInput(prev=>({...prev,[key]:value}));
    }

    const addNewDress=(serial:number,parentTable:string|null)=>{
            const exist = dressInput[serial]?.trim();
            const formData = new FormData();

            if(!exist) return;

            const copy = productDetails[serial];
            const update = {...copy,class:[...copy.class,{id:null,class:exist,parenttable:null}]};

            const newProductDetails = productDetails;
            
            newProductDetails[serial] = update;

            setProductDetails(newProductDetails);

            formData.append("class",exist);
            formData.append("parenttable",parentTable || "");

            addDressClass.mutate(formData);

            setDressInput(prev=>({...prev,[serial]:""}))
    }

    const removeDress=(primaryIndex:number,secondaryIndex:number,databaseId:string|null)=>{
        const copy = productDetails;
        const update = copy.map((items,index)=>{
            if(index === primaryIndex){
                return {...items,class:items.class.filter((_,subIndex)=> subIndex !== secondaryIndex)}
            }else{
                return items;
            }
        });

        setProductDetails(update)

        removeDressClass.mutate(databaseId ?? "");
    }

    const removeDressCategory=(serial:string | null)=>{
        const copy = productDetails;
        const update = copy.filter((items)=> items.id !== serial);

        setProductDetails(update);

        removeCategory.mutate(serial);
    }

    const toggleAccordion=(serial:number)=>{
        const update = accordionArr.includes(serial) ? 
        accordionArr.filter(items=> items !== serial) : [...accordionArr,serial]

        setAccordionArr(update)
    }

    const toggleEditArray=(primaryIndex:number,secondaryIndex:number)=>{
        const copy = editArray;
        const match= copy.some((items)=>items[primaryIndex] === secondaryIndex);
        let update;

        if(match){
            update = copy.filter((items)=>items[primaryIndex] !== secondaryIndex)
        }else{
            update = [...copy,{[primaryIndex]:secondaryIndex}]
        }

        setEditArray(update);
    }

    const handleDressStyleInput=(primaryIndex:number,secondaryIndex:number,event: React.ChangeEvent<HTMLInputElement>)=>{
        const {value} = event.target;
        const copy = productDetails;

        const update = copy.map((items,index)=> index === primaryIndex ? {
            ...items,class:items.class.map((subItems,subIndex)=> subIndex === secondaryIndex ? {...subItems,class:value} : subItems)
        } : items);

        setProductDetails(update);
    }

    const toggleCategoryArr=(serial:number)=>{
        const update = categoryArray?.includes(serial) ?
        categoryArray.filter(items=>items !== serial) : [...categoryArray,serial];

        setCategoryArray(update);
    }

    const handleCategoryInput=(primaryIndex:number,event:React.ChangeEvent<HTMLInputElement>)=>{
        const {value} = event.target;
        const copy = productDetails;
        const update = copy.map((items,index)=> index === primaryIndex ? {...items,category:value} : items);

        setProductDetails(update);
    }

    const updateDressCategory=(indexNum:string|null,category:string|null)=>{
        const copy = productDetails.filter((items)=>items.id == indexNum)[0];
        const formData = new FormData();
        const wrap = {
            id: copy.id,
            category: copy.category
        }

        formData.append("id",wrap?.id ?? "");
        formData.append("category",wrap?.category ?? "");

        updateCategory.mutate(formData);
    }

    const editDressClass=(primaryIndex:number,subIndex:number)=>{
        const copy = productDetails[primaryIndex].class[subIndex];
        const formData = new FormData();

        formData.append("id", copy.id ?? "");
        formData.append("class",copy.class ?? "");

        updateDressClass.mutate(formData);
    }

    const nextPage=(indexNum:string | null)=>{
        const info = {parenttable:indexNum ?? "",currentid:null}
        const encoded = encodeURIComponent(JSON.stringify(info));
        router.push(`/dashboard/products/${encoded}`)
    }
    return (
        <>
        <section className="mt-5 px-5">
            <div className="flex flex-row justify-end gap-x-5">
                <div className="h-[50px] w-[40%]">
                    <input type="text" className={`${manrope.className} h-full w-full border-1 border-gray-300 rounded-lg px-5 capitalize text-black font-medium text-base placeholder:text-black/20 focus:outline-gray-500/50`} placeholder="add your category" ref={categoryRef}/>
                </div>

                <div>
                    <button type="button" className={`${manrope.className} text-base font-bold uppercase h-[50px] px-5 bg-[#2ecc71] rounded-lg text-white shadow-[5px_5px_2px_#16a085] transition-all duration-200 ease-linear hover:shadow-[1px_1px_2px_#16a085] hover:scale-95 hover:cursor-pointer active:bg-[#16a085]`} onClick={addNewCategory}>
                        add new
                    </button>
                </div>
            </div>
        </section>

        <section className="px-5 mt-5 py-20">
            <div className="grid grid-cols-3 gap-x-5">
                <div className="col-span-2 rounded-xl px-5 space-y-[30px]">
                    {
                        productDetails.map((items,index)=>{
                            return <div className={`relative py-10 transition-all duration-500 ease-linear ${accordionArr.includes(index) ?"max-h-[450px] overflow-y-scroll dashboardCategoryScrollbar":"max-h-[90px] overflow-hidden"}`} key={index}>
                                <span className="absolute right-5 top-5 text-rose-300 rotate-[90deg] transition-all duration-200 ease-linear hover:rotate-0 hover:text-rose-500 hover:cursor-pointer" onClick={()=>{removeDressCategory(items.id)}}>
                                    <ImCross />
                                </span>

                        <div className="grid grid-cols-2 items-center border border-black border-t-0 border-r-0 border-l-0 py-2">
                            <div className="flex justify-between items-center">
                                <div className="relative">
                                    <div className="absolute top-[-40px]">
                                        
                                        {
                                            categoryArray.includes(index) ?
                                            <button className="p-[10px] bg-black text-white opacity-10 transition-all duration-200 ease-linear hover:opacity-100 hover:cursor-pointer" onClick={()=>{
                                                toggleCategoryArr(index)
                                                updateDressCategory(items.id,items.category)
                                                }}>
                                                <FaCheck />
                                            </button>:
                                            <button className="p-[10px] bg-black text-white opacity-10 transition-all duration-200 ease-linear hover:opacity-100 hover:cursor-pointer" onClick={()=>{toggleCategoryArr(index)}}>
                                                <MdModeEditOutline />
                                            </button>
                                        }
                                    </div>
                                    {
                                        categoryArray.includes(index) ?
                                        <input type="text" value={items.category} className="border border-black/20 rounded-lg w-full px-4 focus:outline-black/40" onChange={(event)=>{handleCategoryInput(index,event)}}/>:
                                        <h2 className={`${anton.className} uppercase text-2xl font-bold bg-black text-white rounded-xl px-5`} >
                                            {items.category}
                                        </h2>
                                    }
                                    
                                </div>
                                
                                <div>
                                    <button className="hover:cursor-pointer" onClick={()=>{toggleAccordion(index)}}>
                                    {
                                        !accordionArr.includes(index) ?
                                        <GiHamburgerMenu />:
                                        <ImCross />
                                    }
                                </button>
                                </div>
                            </div>
            
                            <div className="flex flex-row items-center justify-end gap-x-3">
                                <div className="h-[20px]">
                                    <input type="text" className={`${manrope.className} h-full w-full border border-black/20 rounded-md placeholder:text-sm pl-4 focus:outline-black/40 text-black/40 font-medium`} placeholder="New dress class" value={dressInput[index] || ""} onChange={(event)=>{handleDressInput(index,event)}}/>
                                </div>
                                <div>
                                    <button className={`${manrope.className} h-5 flex items-center px-4 border border-black rounded-md text-black font-bold hover:cursor-pointer transition-all duration-200 ease-linear hover:bg-black hover:text-white`} onClick={()=>{addNewDress(index,items.id)}}>
                                        add
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="mt-5">
                            <table className="table-fixed w-full border border-black/10">
                                <thead className="bg-black text-white">
                                    <tr className={`${manrope.className} text-xl text-left capitalize`}>
                                        <th className="px-4 border border-white border-t-0 border-b-0 border-l-0 w-[10%]">
                                            id
                                        </th>
                                        <th className="px-4 w-[60%]">
                                            dress style
                                        </th>
                                        <th className="px-4 border border-black border-t-0 border-b-0 border-l-0 w-[30%]">

                                        </th>
                                    </tr>
                                </thead>

                                <tbody>
                                    {
                                        items.class?.map((subItems,subIndex)=>{
                                            return <tr className={`${manrope.className} font-medium text-base h-[60px] capitalize text-black/60 even:bg-white`} key={subIndex}>
                                        <td className="px-4">
                                            {subIndex}
                                        </td>
                                        <td className="px-4">
                                            {
                                                editArray.some((items)=>items[index] === subIndex)?
                                                <input type="text" value={subItems.class ?? ""} className="capitalize border border-black/20 rounded-lg px-2 w-full"
                                                onChange={(event)=>{handleDressStyleInput(index,subIndex,event)}}
                                                />:
                                                subItems.class
                                            }
                                            
                                        </td>
                                        <td className="px-4 text-right space-x-4">
                                            <button className="p-2 bg-[#2ecc71] rounded-xl text-white transition-all duration-200 ease-linear hover:bg-[#27ae60] shadow-[3px_3px_2px_#2ecc71]" onClick={()=>{nextPage(subItems.id)}}>
                                                <FaPersonHalfDress />
                                            </button>
                                            {
                                                editArray.some((items)=>items[index] === subIndex)?
                                                 <button className="p-2 bg-[#f39c12] rounded-xl text-white transition-all duraiton-200 ease-linear hover:bg-[#e67e22] shadow-[3px_3px_2px_#f39c12]" onClick={()=>{
                                                    toggleEditArray(index,subIndex);
                                                    editDressClass(index,subIndex);
                                                    }}>
                                                <FaCheck />
                                                </button>:
                                                 <button className="p-2 bg-[#f39c12] rounded-xl text-white transition-all duraiton-200 ease-linear hover:bg-[#e67e22] shadow-[3px_3px_2px_#f39c12]" onClick={()=>{toggleEditArray(index,subIndex)}}>
                                                <MdModeEditOutline />
                                                </button>
                                            }
                                           
                                            <button className="p-2 bg-[#e74c3c] rounded-xl text-white transition-all duration-200 ease-linear hover:bg-[#c0392b] shadow-[3px_3px_2px_#e74c3c]" onClick={()=>{removeDress(index,subIndex,subItems.id)}}>
                                                <MdDelete />
                                            </button>
                                        </td>
                                    </tr>
                                        })
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                        })
                    }
                </div>

                <div>

                </div>
            </div>
        </section>
        </>
    )
}