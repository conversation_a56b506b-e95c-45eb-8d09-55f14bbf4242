import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";
import { isUUID } from "validator";

const prisma = new PrismaClient();

export async function GET(req: NextRequest){
    const {searchParams} = new URL(req.url);
    const search = searchParams.get("search")?.toString();
    const selectedData = {
        id:true,
        category:true,
        dressclass:{
            select:{
                id:true,
                class:true
            }
        }
    }
    let getData;
    let totalPage;

    if(search){
        const condition:any[] = [];

        if(isUUID(search)){
            condition.push({id:search})
        }

        condition.push({category:{contains:search,mode:"insensitive"}});
        condition.push({dressclass:{some:{class:{contains:search,mode:"insensitive"}}}})

        getData = await prisma.dresscategory.findMany({
            where:{
                OR:condition
            },
            select:selectedData
        })
    }else{
        totalPage = Math.ceil(await prisma.dressclass.count());

        getData = await prisma.dresscategory.findMany({
            select:selectedData
        })
    }

    return NextResponse.json({data:getData,totalPage});
}

// first count how many rows exist in dressclass