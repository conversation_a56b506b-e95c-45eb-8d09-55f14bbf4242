import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";
import { contains, isUUID } from "validator";

const prisma = new PrismaClient();

export async function GET(req: NextRequest){
    const {searchParams} = new URL(req.url);
    const search = searchParams.get("search")?.toString();
    const offset = searchParams.get("offset");
    const selectedData = {
        id : true,
        class: true,
        dresscategory:{
            select:{
                category:true,
                id:true
            }
        }
    }
    let totalRows = await prisma.dressclass.count();
    let totalPage;
    let getData;

    if(search && search != "null"){
        const condition:any[] = [];
        if(isUUID(search)){
            condition.push({dresscategory:{id:search}})
        }

        condition.push({dresscategory:{category:{contains:search,mode:'insensitive'}}});
        condition.push({class:{contains:search,mode:'insensitive'}});

        getData = await prisma.dressclass.findMany({
            where:{
                OR:condition
            },
            select:{
                id:true,
                class:true,
                dresscategory:{
                    select:{
                        id:true,
                        category:true
                    }
                }
            },
            take: 4,
            skip: Number(offset)
        });

        totalRows = await prisma.dressclass.count({
            where:{
                OR:condition
            }
        });

        totalPage = Math.ceil(totalRows/4)
        
    }else{
        getData = await prisma.dressclass.findMany({
            select:{
                id: true,
                class: true,
                dresscategory:{
                    select:{
                        id: true,
                        category: true
                    }
                }
            },
            take: 4,
            skip: Number(offset)
        });

        totalPage = Math.ceil(totalRows/4)
    }

    return NextResponse.json({data:getData,totalPage});
}