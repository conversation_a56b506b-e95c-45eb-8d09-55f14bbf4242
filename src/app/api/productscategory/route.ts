import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";
import { isUUID } from "validator";

const prisma = new PrismaClient();

export async function GET(req: NextRequest){
    const {searchParams} = new URL(req.url);
    const search = searchParams.get("search")?.toString();
    const offset = searchParams.get("currentpage");
    const selectedData = {
        id : true,
        class: true,
        dresscategory:{
            select:{
                category:true,
                id:true
            }
        }
    }
    const totalRows = await prisma.dressclass.count();
    const totalPage = Math.ceil(totalRows/4);
    let getData;

    if(search || offset){
        getData = await prisma.dressclass.findMany({
            select:{
                id:true,
                class: true,
                dresscategory: {
                    select:{
                        id: true,
                        category:true
                    }
                }
            },
            take:4,
            skip: Number(offset)
        })
    }else{
        getData = await prisma.dressclass.findMany({
            select:{
                id: true,
                class: true,
                dresscategory:{
                    select:{
                        id: true,
                        category: true
                    }
                }
            },
            take: 4,
            skip: Number(offset),
        })
    }

    return NextResponse.json({data:getData,totalPage});
}