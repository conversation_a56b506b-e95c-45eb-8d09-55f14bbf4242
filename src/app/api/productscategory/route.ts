import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";
import { isUUID } from "validator";

const prisma = new PrismaClient();

export async function GET(req: NextRequest){
    const {searchParams} = new URL(req.url);
    const search = searchParams.get("search")?.toString();
    const selectedData = {
        id : true,
        class: true,
        dresscategory:{
            select:{
                category:true,
                id:true
            }
        }
    }
    const totalRows = await prisma.dressclass.count();
    const totalPage = Math.ceil(totalRows/4);
    let getData;

    if(search){
        const condition:any[] = [];

        if(isUUID(search)){
            condition.push({id:search})
        }

        condition.push({category:{contains:search,mode:"insensitive"}});
        condition.push({dressclass:{some:{class:{contains:search,mode:"insensitive"}}}})

        getData = await prisma.dressclass.findMany({
            where:{
                OR:condition
            },
            select:selectedData
        })
    }else{
        getData = await prisma.dressclass.findMany({
            select:{
                id: true,
                class: true,
                dresscategory:{
                    select:{
                        id: true,
                        category: true
                    }
                }
            },
            take: 5,
            skip: 0
        })
    }

    return NextResponse.json({data:getData,totalPage});
}