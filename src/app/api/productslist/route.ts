import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";
import { isUUID } from "validator";

const prisma = new PrismaClient();

export async function GET(req: NextRequest){
    const {searchParams} = new URL(req.url);
    const relationalId = searchParams.get("relationid")?.toString();
    const search = searchParams.get("search")?.toString();
    const selectedData = {
        id: true,
        title: true,
        items: true
    }
    let getData;

    if(search){
        const condition:any[] = [];

        if(isUUID(search)){
            condition.push({id:search})
        }

        condition.push({title:{contains:search, mode:"insensitive"}});
        condition.push({items:{contains:search, mode:"insensitive"}});

        getData = await prisma.products.findMany({
            where:{
                AND:[
                    {parenttable:relationalId},
                    {
                        OR:condition
                    }
                ]
            },
            select:selectedData
        })
    }else{
        getData = await prisma.products.findMany({
            where:{
                parenttable:relationalId
            },
            select:selectedData
        })
    }

    return NextResponse.json(getData);
}