import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";
import { isUUID } from "validator";

const prisma = new PrismaClient();

export async function GET(req: NextRequest){
    const {searchParams} = new URL(req.url);
    const relationalId = searchParams.get("relationid")?.toString();
    const search = searchParams.get("search")?.toString();
    const offset = searchParams.get("offset");

    const selectedData = {
        id: true,
        title: true,
        items: true
    }
    let getData;
    let totalRows = await prisma.products.count();
    let totalPage = Math.ceil(totalRows/6);

    if(search && search != "null"){
        const condition:any[] = [];

        if(isUUID(search)){
            condition.push({id:search})
        }

        condition.push({title:{contains:search, mode:"insensitive"}});
        condition.push({items:{contains:search, mode:"insensitive"}});

        getData = await prisma.products.findMany({
            where:{
                AND:[
                    {parenttable:relationalId},
                    {
                        OR:condition
                    }
                ]
            },
            select:selectedData,
            take:6,
            skip:Number(offset)
        });

        totalRows = await prisma.products.count({
            where:{
                AND:[
                    {parenttable:relationalId},
                    {
                        OR:condition
                    }
                ]
            }
        });

        totalPage = Math.ceil(totalRows/6);
    }else{
        getData = await prisma.products.findMany({
            where:{
                parenttable:relationalId
            },
            select:selectedData,
            take: 6,
            skip: Number(offset)
        });
    }

    return NextResponse.json({data:getData,totalPage});
}