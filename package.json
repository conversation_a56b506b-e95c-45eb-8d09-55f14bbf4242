{"name": "shopco", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^6.11.0", "@tanstack/react-query": "^5.82.0", "axios": "^1.10.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "validator": "^13.15.15"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/validator": "^13.15.2", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.5.6", "prisma": "^6.11.0", "tailwindcss": "^4.1.11", "typescript": "^5"}}